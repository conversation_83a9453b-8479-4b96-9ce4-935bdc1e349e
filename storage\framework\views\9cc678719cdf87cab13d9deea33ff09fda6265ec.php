

<?php $__env->startSection('title', 'แดชบอร์ด - Admin Panel'); ?>

<?php $__env->startSection('content'); ?>
<?php
    $stats = \App\Helpers\DashboardHelper::getStatistics();
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-tachometer-alt me-2"></i>แดชบอร์ด
                    </h1>
                    <p class="text-muted">ภาพรวมระบบจัดการเว็บไซต์ SoloShop</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item active">แดชบอร์ด</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row">
                <!-- Services Card -->
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3><?php echo e(\App\Helpers\DashboardHelper::formatNumber($stats['services']['total'])); ?></h3>
                            <p>บริการทั้งหมด</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <a href="<?php echo e(route('admin.services.index')); ?>" class="small-box-footer">
                            จัดการบริการ <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Packages Card -->
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3><?php echo e(\App\Helpers\DashboardHelper::formatNumber($stats['packages']['total'])); ?></h3>
                            <p>แพ็กเกจทั้งหมด</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <a href="<?php echo e(route('admin.packages.index')); ?>" class="small-box-footer">
                            จัดการแพ็กเกจ <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Activities Card -->
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3><?php echo e(\App\Helpers\DashboardHelper::formatNumber($stats['activities']['total'])); ?></h3>
                            <p>กิจกรรมทั้งหมด</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-images"></i>
                        </div>
                        <a href="<?php echo e(route('admin.activities.index')); ?>" class="small-box-footer">
                            ดูรายละเอียด <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>

                <!-- Contacts Card -->
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3><?php echo e(\App\Helpers\DashboardHelper::formatNumber($stats['contacts']['unread'])); ?></h3>
                            <p>ข้อความใหม่ (<?php echo e(\App\Helpers\DashboardHelper::formatNumber($stats['contacts']['total'])); ?> ทั้งหมด)</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <a href="<?php echo e(route('admin.contacts.index')); ?>" class="small-box-footer">
                            ดูรายละเอียด <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>